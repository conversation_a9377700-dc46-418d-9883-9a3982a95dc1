import requests
import openai
import tiktoken
import base64
import os
import mimetypes
import PyPDF2
import io
import tempfile
import urllib.request
import boto3
from django.conf import settings
from common.logger import Logger
from apps.integration.utils import ConfigHelper
from common.constants import MAX_FILE_SIZE
from apps.llm_manager.utils.file_types import (
    is_supported_file_type, is_vision_file_type, is_document_file_type,
    get_mime_type
)

try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False

logger = Logger(__name__)

class BaseAIHandler:
    def __init__(self, api_key, model, base_url=None, workspace_id=None):
        openai.api_key = api_key
        if base_url:
            openai.base_url = base_url
        else:
            openai.base_url = None
        self.model = model
        self.temp_files = []  # Track temporary files for cleanup

    def count_tokens(self, messages):
        """
        Counts the number of tokens in the given messages.
        """
        try:
            encoding = tiktoken.encoding_for_model(self.model)
            num_tokens = sum(len(encoding.encode(msg.get("content", ""))) for msg in messages if "content" in msg)
            return num_tokens
        except Exception as e:
            logger.error(f"Error counting tokens: {e}")
            return 0

    def send_text_request(self, prompts, response_count=1, max_tokens=None, stream=True):
        """
        Sends a request to the AI API to get a response based on the provided prompt.
        """
        try:
            response = openai.chat.completions.create(
                model=self.model,
                messages=prompts,
                max_tokens=max_tokens,
                n=response_count,
                temperature=0.7,
                stream=stream
            )
            return response
        except requests.exceptions.ConnectionError as e:
            logger.error(f"Connection error sending text request: {e}")
            return None
        except openai.OpenAIError as e:
            logger.error(f"OpenAI API error: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error sending text request: {e}")
            return None

    def generate_image(self, prompt, size="1024x1792", quality="standard", style="natural", n=1):
        """
        Generates an image based on the provided prompt.

        Args:
            prompt (str): The prompt to generate an image from
            size (str): Image size (default: "1024x1792")
            quality (str): Image quality (default: "standard")
            style (str): Image style - "natural" for realistic, "vivid" for stylized (default: "natural")
            n (int): Number of images to generate (default: 1)
        """
        try:
            response = openai.images.generate(
                model=self.model,
                prompt=prompt,
                size=size,
                quality=quality,
                style=style,
                n=n
            )

            return response

        except requests.exceptions.ConnectionError as e:
            logger.error(f"Connection error generating image: {e}")
            return None
        except openai.OpenAIError as e:
            logger.error(f"OpenAI API error: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error generating image: {e}")
            return None

    def encode_file_to_base64(self, file_path):
        try:
            with open(file_path, "rb") as file:
                return base64.b64encode(file.read()).decode('utf-8')
        except Exception as e:
            logger.error(f"Error encoding file to base64: {e}")
            return None

    def encode_image_to_base64(self, file_path):
        return self.encode_file_to_base64(file_path)

    def is_https_url(self, file_path):
        """
        Check if the file path is an HTTPS URL.

        Args:
            file_path (str): Path or URL to check

        Returns:
            bool: True if it's an HTTPS URL, False otherwise
        """
        if not isinstance(file_path, str):
            return False

        return file_path.startswith('https://')

    def download_from_https(self, file_path):
        """
        Download a file from HTTPS URL to a temporary location.

        Args:
            file_path (str): HTTPS URL

        Returns:
            str: Path to the downloaded temporary file
        """
        try:
            # Create a temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False)
            temp_path = temp_file.name
            temp_file.close()

            # Track the temporary file for cleanup
            self.temp_files.append(temp_path)

            # Download the file from HTTPS URL
            logger.info(f"Downloading from HTTPS URL: {file_path}")
            urllib.request.urlretrieve(file_path, temp_path)

            # Verify the file was downloaded successfully
            if os.path.exists(temp_path) and os.path.getsize(temp_path) > 0:
                logger.info(f"Successfully downloaded file: {file_path} -> {temp_path}")
                return temp_path
            else:
                logger.error(f"Downloaded file is empty or doesn't exist: {temp_path}")
                return None

        except Exception as e:
            logger.error(f"Error downloading file from HTTPS: {e}")
            # Clean up the temp file if it was created but download failed
            try:
                if 'temp_path' in locals() and os.path.exists(temp_path):
                    os.unlink(temp_path)
                    if temp_path in self.temp_files:
                        self.temp_files.remove(temp_path)
            except:
                pass
            return None

    def cleanup_temp_files(self):
        """
        Clean up all temporary files created during processing.
        """
        for temp_file in self.temp_files:
            try:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
                    logger.info(f"Cleaned up temporary file: {temp_file}")
            except Exception as e:
                logger.warning(f"Error cleaning up temporary file {temp_file}: {e}")

        # Clear the list
        self.temp_files = []

    def validate_file(self, file_path):
        """
        Validates a file for size and content.

        Args:
            file_path (str): Path to the file

        Returns:
            tuple: (is_valid, error_message)
        """
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                return False, f"File not found: {file_path}"

            # Check file size
            file_size = os.path.getsize(file_path)
            max_size = getattr(settings, 'MAX_UPLOAD_SIZE', MAX_FILE_SIZE)
            if file_size > max_size:
                return False, f"File size exceeds maximum allowed size of {max_size/1024/1024}MB"

            # Check file extension
            file_ext = os.path.splitext(file_path)[1].lower().replace('.', '')
            if not is_supported_file_type(file_ext):
                return False, f"Unsupported file type: {file_ext}"

            # Additional content validation for security
            if MAGIC_AVAILABLE:
                try:
                    # Use python-magic to detect file type from content
                    mime = magic.Magic(mime=True)
                    detected_mime = mime.from_file(file_path)
                    expected_mime = get_mime_type(file_ext)

                    # If detected MIME type doesn't match expected type, reject the file
                    if expected_mime and detected_mime and not detected_mime.startswith(expected_mime.split('/')[0]):
                        return False, f"File content doesn't match extension: detected {detected_mime}, expected {expected_mime}"
                except Exception as e:
                    logger.warning(f"Error during content validation: {e}")
            else:
                # Basic validation using mimetypes module
                guessed_type = mimetypes.guess_type(file_path)[0]
                expected_mime = get_mime_type(file_ext)

                if guessed_type and expected_mime and guessed_type != expected_mime:
                    logger.warning(f"Possible file type mismatch: guessed {guessed_type}, expected {expected_mime}")

            return True, None
        except Exception as e:
            logger.error(f"Error validating file: {e}")
            return False, f"Error validating file: {str(e)}"

    def upload_file_to_openai(self, file_path, purpose="assistants"):
        """
        Uploads a file to OpenAI's File API.

        Args:
            file_path (str): Path to the file
            purpose (str): Purpose of the file (default: "assistants")

        Returns:
            dict: OpenAI file object or None if upload failed
        """
        try:
            # Validate file before uploading
            is_valid, error_message = self.validate_file(file_path)
            if not is_valid:
                logger.error(error_message)
                return None

            # Check if file is a document type
            file_ext = os.path.splitext(file_path)[1].lower().replace('.', '')
            if not is_document_file_type(file_ext):
                logger.warning(f"File type {file_ext} is not a document type supported by OpenAI File API")
                return None

            # Upload file to OpenAI
            try:
                with open(file_path, "rb") as file:
                    response = openai.files.create(
                        file=file,
                        purpose=purpose
                    )

                logger.info(f"File uploaded to OpenAI: {file_path}, file_id: {response.id}")
                return response
            except openai.BadRequestError as e:
                # Handle specific API errors
                error_message = str(e)
                if "purpose" in error_message and "assistants" in error_message:
                    # Try again with a different purpose if "assistants" is not supported
                    logger.warning(f"Purpose 'assistants' not supported, trying with 'fine-tune'")
                    with open(file_path, "rb") as file:
                        response = openai.files.create(
                            file=file,
                            purpose="fine-tune"
                        )
                    logger.info(f"File uploaded to OpenAI with purpose 'fine-tune': {file_path}, file_id: {response.id}")
                    return response
                else:
                    raise e

        except openai.OpenAIError as e:
            logger.error(f"OpenAI API error uploading file: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error uploading file: {e}")
            return None

    def delete_file_from_openai(self, file_id):
        """
        Deletes a file from OpenAI's File API.

        Args:
            file_id (str): ID of the file to delete

        Returns:
            bool: True if deletion was successful, False otherwise
        """
        try:
            response = openai.files.delete(file_id)
            logger.info(f"File deleted from OpenAI: {file_id}")
            return response.deleted
        except openai.OpenAIError as e:
            logger.error(f"OpenAI API error deleting file: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error deleting file: {e}")
            return False

    def get_file_info_from_openai(self, file_id):
        """
        Retrieves information about a file from OpenAI's File API.

        Args:
            file_id (str): ID of the file to retrieve

        Returns:
            dict: File information or None if retrieval failed
        """
        try:
            response = openai.files.retrieve(file_id)
            return response
        except openai.OpenAIError as e:
            logger.error(f"OpenAI API error retrieving file: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error retrieving file: {e}")
            return None

    def list_files_from_openai(self, purpose=None):
        """
        Lists all files from OpenAI's File API.

        Args:
            purpose (str, optional): Filter files by purpose

        Returns:
            list: List of files or None if retrieval failed
        """
        try:
            if purpose:
                response = openai.files.list(purpose=purpose)
            else:
                response = openai.files.list()
            return response.data
        except openai.OpenAIError as e:
            logger.error(f"OpenAI API error listing files: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error listing files: {e}")
            return None

    def download_file_content_from_openai(self, file_id, output_path=None):
        """
        Downloads file content from OpenAI's File API.

        Args:
            file_id (str): ID of the file to download
            output_path (str, optional): Path to save the file to

        Returns:
            bytes or str: File content as bytes or path to saved file
        """
        try:
            response = openai.files.content(file_id)
            content = response.read()

            if output_path:
                with open(output_path, "wb") as f:
                    f.write(content)
                logger.info(f"File downloaded from OpenAI: {file_id} -> {output_path}")
                return output_path
            else:
                return content
        except openai.OpenAIError as e:
            logger.error(f"OpenAI API error downloading file: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error downloading file: {e}")
            return None

    def extract_text_from_pdf(self, file_path):
        """
        Extracts text from a PDF file.

        Args:
            file_path (str): Path to the PDF file

        Returns:
            str: Extracted text or None if extraction failed
        """
        try:
            text = ""
            with open(file_path, "rb") as file:
                pdf_reader = PyPDF2.PdfReader(file)
                num_pages = len(pdf_reader.pages)

                # Extract text from each page
                for page_num in range(num_pages):
                    page = pdf_reader.pages[page_num]
                    text += page.extract_text() + "\n\n"

                    # Limit text length to avoid token limits
                    if len(text) > 100000:  # Approximately 25,000 tokens
                        text += f"\n\n[Text truncated. PDF has {num_pages} pages in total, showing content from the first {page_num+1} pages.]\n"
                        break

            return text if text.strip() else "No text could be extracted from the PDF."
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {e}")
            return None

    def prepare_vision_message(self, prompts, file_paths):
        try:
            for i in range(len(prompts) - 1, -1, -1):
                if prompts[i]["role"] == "user":
                    last_user_msg_index = i
                    break
            else:
                return prompts

            if isinstance(prompts[last_user_msg_index]["content"], str):
                prompts[last_user_msg_index]["content"] = [
                    {"type": "text", "text": prompts[last_user_msg_index]["content"]}
                ]
            elif not isinstance(prompts[last_user_msg_index]["content"], list):
                prompts[last_user_msg_index]["content"] = [
                    {"type": "text", "text": str(prompts[last_user_msg_index]["content"])}
                ]

            for file_path in file_paths:
                if not os.path.exists(file_path):
                    logger.warning(f"File not found: {file_path}")
                    continue

                file_ext = os.path.splitext(file_path)[1].lower().replace('.', '')
                if not is_supported_file_type(file_ext):
                    logger.warning(f"Unsupported file type: {file_ext}")
                    continue

                # Encode the file to base64
                base64_file = self.encode_file_to_base64(file_path)
                if not base64_file:
                    logger.warning(f"Failed to encode file: {file_path}")
                    continue

                # Get the correct MIME type
                mime_type = get_mime_type(file_ext)

                # Only image files can be included directly in the message
                if is_vision_file_type(file_ext):
                    prompts[last_user_msg_index]["content"].append({
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:{mime_type};base64,{base64_file}",
                            "detail": "high"
                        }
                    })
                    logger.info(f"Added image file to message: {file_path}")
                # For document files, we'll handle them using the OpenAI File API
                # in the send_text_request_with_files method
                else:
                    logger.info(f"Document file detected: {file_path}. This will be handled by the OpenAI File API.")
                    # Add a note about the file
                    file_name = os.path.basename(file_path)
                    prompts[last_user_msg_index]["content"].append({
                        "type": "text",
                        "text": f"\n[Document file: {file_name}]\n"
                    })

            return prompts
        except Exception as e:
            logger.error(f"Error preparing vision message: {e}")
            return prompts

class OpenAIHandler(BaseAIHandler):
    def __init__(self, model):
        config_helper = ConfigHelper()
        api_key = config_helper.get_integration_setting('openai', 'api_key', default=settings.OPENAI_API_KEY)
        super().__init__(api_key=api_key, model=model)

    def send_text_request_with_files(self, prompts, file_paths, response_count=1, max_tokens=None, stream=True):
        try:
            if not file_paths:
                # If no files, just use the regular text request
                return self.send_text_request(prompts, response_count, max_tokens, stream)

            # Validate files before proceeding
            valid_files = []
            invalid_files = []
            vision_files = []
            document_files = []
            uploaded_file_ids = []
            temp_files_created = False
            local_file_paths = []

            for file_path in file_paths:
                local_path = file_path

                # Check if the file is an HTTPS URL and download it if needed
                if self.is_https_url(file_path):
                    logger.info(f"Downloading file from HTTPS: {file_path}")
                    local_path = self.download_from_https(file_path)
                    temp_files_created = True

                    if not local_path:
                        invalid_files.append((os.path.basename(file_path), "Failed to download from HTTPS"))
                        continue

                local_file_paths.append(local_path)

                if not os.path.exists(local_path):
                    invalid_files.append((os.path.basename(local_path), "File not found"))
                    continue

                # Validate file
                is_valid, error_message = self.validate_file(local_path)
                if not is_valid:
                    invalid_files.append((os.path.basename(local_path), error_message))
                    continue

                valid_files.append(local_path)
                file_ext = os.path.splitext(local_path)[1].lower().replace('.', '')

                # Categorize files by type
                if is_vision_file_type(file_ext):
                    vision_files.append(local_path)
                elif is_document_file_type(file_ext):
                    document_files.append(local_path)

            # If there are invalid files, log warnings
            if invalid_files:
                for file_name, error in invalid_files:
                    logger.warning(f"Invalid file '{file_name}': {error}")

            # If no valid files, raise an error
            if not valid_files:
                error_msg = "No valid files to process"
                logger.error(error_msg)
                raise ValueError(error_msg)

            # Find the last user message index
            last_user_msg_index = -1
            for i in range(len(prompts) - 1, -1, -1):
                if prompts[i]["role"] == "user":
                    last_user_msg_index = i
                    break

            # Process document files
            for doc_file in document_files:
                file_name = os.path.basename(doc_file)
                file_ext = os.path.splitext(doc_file)[1].lower().replace('.', '')

                # For PDF files, try to extract text first
                if file_ext == 'pdf':
                    pdf_text = self.extract_text_from_pdf(doc_file)
                    if pdf_text:
                        # Add the PDF text to the prompt
                        if last_user_msg_index >= 0:
                            if isinstance(prompts[last_user_msg_index]["content"], str):
                                prompts[last_user_msg_index]["content"] += f"\n\nContent of {file_name}:\n```\n{pdf_text}\n```\n"
                            elif isinstance(prompts[last_user_msg_index]["content"], list):
                                prompts[last_user_msg_index]["content"].append({
                                    "type": "text",
                                    "text": f"\n\nContent of {file_name}:\n```\n{pdf_text}\n```\n"
                                })
                        continue

                # For PDFs (when text extraction fails) and other complex document types, upload to OpenAI File API
                if file_ext in ['pdf', 'docx', 'xlsx', 'pptx']:
                    file_response = self.upload_file_to_openai(doc_file)
                    if file_response:
                        uploaded_file_ids.append(file_response.id)

                        # Add a note about the uploaded file to the prompt
                        if last_user_msg_index >= 0:
                            if isinstance(prompts[last_user_msg_index]["content"], str):
                                prompts[last_user_msg_index]["content"] += f"\n[Uploaded document: {file_name}]\n"
                            elif isinstance(prompts[last_user_msg_index]["content"], list):
                                prompts[last_user_msg_index]["content"].append({
                                    "type": "text",
                                    "text": f"\n[Uploaded document: {file_name}]\n"
                                })

                # For simple text files, read the content and include it directly
                elif file_ext in ['txt', 'csv', 'json', 'md']:
                    try:
                        with open(doc_file, 'r', encoding='utf-8') as f:
                            file_content = f.read()

                        # Add the file content to the prompt
                        if last_user_msg_index >= 0:
                            if isinstance(prompts[last_user_msg_index]["content"], str):
                                prompts[last_user_msg_index]["content"] += f"\n\nContent of {file_name}:\n```\n{file_content}\n```\n"
                            elif isinstance(prompts[last_user_msg_index]["content"], list):
                                prompts[last_user_msg_index]["content"].append({
                                    "type": "text",
                                    "text": f"\n\nContent of {file_name}:\n```\n{file_content}\n```\n"
                                })
                    except Exception as e:
                        logger.error(f"Error reading text file {doc_file}: {e}")
                        # Add a note about the error
                        if last_user_msg_index >= 0:
                            if isinstance(prompts[last_user_msg_index]["content"], str):
                                prompts[last_user_msg_index]["content"] += f"\n[Error reading file: {file_name}]\n"
                            elif isinstance(prompts[last_user_msg_index]["content"], list):
                                prompts[last_user_msg_index]["content"].append({
                                    "type": "text",
                                    "text": f"\n[Error reading file: {file_name}]\n"
                                })

            # Prepare the prompt with vision files
            if vision_files:
                enhanced_prompts = self.prepare_vision_message(prompts, vision_files)
            else:
                enhanced_prompts = prompts

            # For document files, we've already uploaded them to OpenAI and added notes to the prompts
            # The OpenAI Chat Completions API doesn't directly support file_ids parameter
            # We'll use the enhanced prompts that include references to the uploaded files

            try:
                # Create chat completion
                response = openai.chat.completions.create(
                    model=self.model,
                    messages=enhanced_prompts,
                    max_tokens=max_tokens,
                    n=response_count,
                    temperature=0.7,
                    stream=stream
                )

                # Store file IDs for potential cleanup later
                if uploaded_file_ids and hasattr(response, 'id'):
                    logger.info(f"Files uploaded for completion {response.id}: {uploaded_file_ids}")

                    # Clean up uploaded files after use
                    # This is optional and can be controlled by a setting
                    cleanup_files = getattr(settings, 'OPENAI_CLEANUP_FILES', True)
                    if cleanup_files:
                        for file_id in uploaded_file_ids:
                            self.delete_file_from_openai(file_id)

                return response
            finally:
                # Clean up temporary files if any were created
                if temp_files_created:
                    self.cleanup_temp_files()

        except ValueError as ve:
            # Clean up temporary files in case of error
            if temp_files_created:
                self.cleanup_temp_files()
            logger.error(f"Validation error: {str(ve)}")
            return None
        except requests.exceptions.ConnectionError as e:
            # Clean up temporary files in case of error
            if temp_files_created:
                self.cleanup_temp_files()
            logger.error(f"Connection error sending text request with files: {e}")
            return None
        except openai.OpenAIError as e:
            # Clean up temporary files in case of error
            if temp_files_created:
                self.cleanup_temp_files()
            logger.error(f"OpenAI API error: {e}")
            return None
        except Exception as e:
            # Clean up temporary files in case of error
            if temp_files_created:
                self.cleanup_temp_files()
            logger.error(f"Unexpected error sending text request with files: {e}")
            return None


class DeepSeekHandler(BaseAIHandler):
    def __init__(self, model):
        config_helper = ConfigHelper()
        api_key = config_helper.get_integration_setting('deepseek', 'api_key', default=settings.DEEPSEEK_API_KEY)
        super().__init__(api_key=api_key, model=model, base_url="https://api.deepseek.com")

class QwenHandler(BaseAIHandler):
    def __init__(self):
        config_helper = ConfigHelper()
        api_key = config_helper.get_integration_setting('qwen', 'api_key', default=settings.QWAN_API_KEY)
        super().__init__(
            api_key=api_key,
            model="qwen-plus",
            base_url="https://dashscope-intl.aliyuncs.com/compatible-mode/v1",
        )

class LamaAIHandler(BaseAIHandler):
    def __init__(self, model):
        config_helper = ConfigHelper()
        api_key = config_helper.get_integration_setting('lama', 'api_key', default=settings.LAMA_API_KEY)
        super().__init__(api_key=api_key, model=model, base_url="https://api.llama-api.com")


class OpenAIImageHandler(BaseAIHandler):
    def __init__(self, model):
        config_helper = ConfigHelper()
        api_key = config_helper.get_integration_setting('openai', 'api_key', default=settings.OPENAI_API_KEY)
        super().__init__(api_key=api_key, model=model)
