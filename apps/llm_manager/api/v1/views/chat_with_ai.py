import uuid
import json
import os
from django.http import StreamingHttpResponse
from django.core.cache import cache
from django.conf import settings
from apps.core.services.system_setting_service import SystemSettingService
from apps.identity.utils.get_user_meta_data import get_device_info
from apps.llm_manager.utils.token_usages_policy import TokenUsagePolicyFactory
from dxh_libraries.translation import gettext_lazy as _
from dxh_libraries.rest_framework import status, Response
from dxh_common.base.base_api_view import BaseApiView, PublicApiView
from dxh_common.logger import Logger
from apps.llm_manager.constants import (
    BLOCK_TIME_FOR_USER, MAX_CHAT_LIMIT_FOR_ANNONYMOUS_USER
)
from apps.payment.services.plan_service import PlanService
from apps.payment.services.subscription_service import SubscriptionService
from apps.llm_manager.api.v1.serializers import PromptSerializer
from apps.llm_manager.utils.response_formater import format_ai_response
from apps.llm_manager.services import Conversation<PERSON>er<PERSON>, AIHandlerFactory, LLMModelsService
from apps.user.services.daily_token_uses_service import DailyTokenUsagesService
from apps.user.services.daily_message_usage_service import DailyMessageUsageService
from apps.llm_manager.services import CustomGPTService
from apps.file_manager.services import FileService
from apps.llm_manager.utils.file_types import is_supported_file_type, get_unsupported_files

logger = Logger(__name__)


class AIConversationView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.ai_factory_service = AIHandlerFactory()
        self.llm_model_service = LLMModelsService()
        self.conversation_service = ConversationService()
        self.message_usage_service = DailyMessageUsageService()
        self.token_usages_service = DailyTokenUsagesService()
        self.token_usage_policy_factory = TokenUsagePolicyFactory()
        self.plan_service = PlanService()
        self.subscription_service = SubscriptionService()
        self.custom_gpt_service = CustomGPTService()
        self.file_service = FileService()

    def post(self, request):
        payload = request.data
        serializer = PromptSerializer(data=payload)
        if not serializer.is_valid():
            first_error_message = serializer.errors[next(iter(serializer.errors))][0]
            result = {
                "message": first_error_message,
                "errors": serializer.errors
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        # # Check subscription status
        # subscription = self.subscription_service.get(user=request.user, status='active')
        # if subscription and subscription.current_period_end < timezone.now():
        #     result = {
        #         "message": "Your subscription has expired. Please renew to continue using premium features.",
        #         "errors": {
        #             "type": "subscription_expired",
        #             "renewal_url": f"{settings.FRONTEND_BASE_URL}/billing/renew/{subscription.plan.id}"
        #         }
        #     }
        #     return Response(result, status=status.HTTP_402_PAYMENT_REQUIRED)


        # Get the requested model
        valid_data = serializer.validated_data
        public_conversation_id = valid_data.get("public_conversation_id", None)
        code = valid_data.get("model")
        model = self.llm_model_service.get(code=code)

        if not model:
            result = {
                "message": "Invalid AI model selected"
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        # Check if user has reached their daily message limit
        can_send, current_count, max_limit = self.message_usage_service.check_daily_limit(request.user)
        logger.info(f"Message limit check for user {request.user.id}: can_send={can_send}, count={current_count}, limit={max_limit}, model={model.code}")

        # Get token usage for combined checks
        total_used_tokens = self.token_usages_service.get_monthly_token_usage(request.user)

        # Get token limit from plan
        if request.user.subscription_type == "free":
            free_plan = self.plan_service.get(code="free")
            token_limit = free_plan.tokens_included if free_plan else 50000
        else:
            subscription = self.subscription_service.get(user=request.user, status='active')
            token_limit = subscription.plan.tokens_included if subscription and subscription.plan else 50000

        logger.info(f"Token usage for user {request.user.id}: used={total_used_tokens}, limit={token_limit}")

        # Check if token limit is exceeded
        token_limit_exceeded = total_used_tokens >= token_limit

        # Case 1: Both limits exceeded - block completely
        if not can_send and token_limit_exceeded:
            result = {
                "message": f"You've hit both your daily message limit ({current_count}/{max_limit}) and your daily token limit ({total_used_tokens}/{token_limit}). Please upgrade your plan to continue using our services."
            }
            logger.warning(f"User {request.user.id} blocked: both message and token limits exceeded")
            return Response(result, status=status.HTTP_429_TOO_MANY_REQUESTS)

        # Case 2: Message limit exceeded but token limit not - fallback to gpt-4o-mini
        elif not can_send and not token_limit_exceeded:
            if model.code != "gpt-4o-mini":
                logger.info(f"Message limit reached for user {request.user.id}. Falling back to gpt-4o-mini.")
                model = self.llm_model_service.get(code="gpt-4o-mini")
                if not model:
                    result = {
                        "message": f"You've hit your daily message limit ({current_count}/{max_limit}). Come back tomorrow or upgrade your plan to keep the conversation going!"
                    }
                    return Response(result, status=status.HTTP_429_TOO_MANY_REQUESTS)
            else:
                # Already using gpt-4o-mini and message limit exceeded - block
                result = {
                    "message": f"You've hit your daily message limit ({current_count}/{max_limit}). Come back tomorrow or upgrade your plan to keep the conversation going!"
                }
                logger.warning(f"User {request.user.id} blocked: message limit exceeded while using gpt-4o-mini")
                return Response(result, status=status.HTTP_429_TOO_MANY_REQUESTS)

        # Case 3: Token limit exceeded but message limit not - handled in token usage policy check below

        # Model already selected above

        # Check for custom GPT
        custom_gpt = None
        custom_gpt_id = valid_data.get("custom_gpt_id")
        if custom_gpt_id:
            if request.user.subscription_type == "free":
                result = {
                    "message": _("Custom GPTs are only available for premium users")
                }
                return Response(result, status=status.HTTP_403_FORBIDDEN)

            custom_gpt = self.custom_gpt_service.get(uuid=custom_gpt_id, user=request.user)
            if not custom_gpt:
                result = {
                    "message": "Custom GPT not found"
                }
                return Response(result, status=status.HTTP_404_NOT_FOUND)

        conversation, user_message, prompt_data = self.conversation_service.create_conversation_and_message(
            request.user, valid_data, custom_gpt, public_conversation_id
        )

        # Get AI handler for the selected model
        ai_handler = self.ai_factory_service.get_handler(model.code)

        # Count tokens for the prompt
        input_tokens = ai_handler.count_tokens(prompt_data)

        # Update the user message with token count and model
        user_message.used_tokens = input_tokens
        user_message.llm_model = model
        user_message.save()

        # Log token usage
        logger.info(f"Input tokens for user {request.user.id}: {input_tokens} tokens with model {model.code}")

        # We already checked if both limits are exceeded above
        # Now we only need to handle the case where token limit is exceeded but message limit is not

        # Check token usage policy
        try:
            # Only check token usage if we're not already using gpt-4o-mini
            if model.code != "gpt-4o-mini":
                # We already counted tokens above, no need to count again

                # Check if this request would exceed the token limit
                if total_used_tokens + input_tokens > token_limit:
                    logger.info(f"Token limit would be exceeded for user {request.user.id}. Falling back to gpt-4o-mini.")
                    model = self.llm_model_service.get(code="gpt-4o-mini")
                    if not model:
                        return Response({
                            "message": f"You've exceeded your daily token limit ({total_used_tokens}/{token_limit}). Please upgrade your plan to continue using premium models."
                        }, status=status.HTTP_403_FORBIDDEN)
                    ai_handler = self.ai_factory_service.get_handler(model.code)
            else:
                # If already using gpt-4o-mini, we don't need to check token limit
                # We already checked if both limits are exceeded above
                pass

        except Exception as e:
            # Log the error
            logger.warning(f"Token usage check exception for user {request.user.id}: {str(e)}")

            # If there's an error, fallback to gpt-4o-mini to be safe
            if model.code != "gpt-4o-mini":
                model = self.llm_model_service.get(code="gpt-4o-mini")
                if not model:
                    return Response({
                        "message": "An error occurred. Please try again with a different model."
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                ai_handler = self.ai_factory_service.get_handler(model.code)

        file_paths = []
        file_types = []
        user_message_data = valid_data.get("message", {})
        if user_message_data and "metadata" in user_message_data and "attachments" in user_message_data["metadata"]:
            for attachment in user_message_data["metadata"]["attachments"]:
                try:
                    file_obj = self.file_service.get(id=attachment["id"])
                    # file_paths.append("https://cdn.pioo.ai/temp.jpg")
                    file_paths.append(file_obj.file.url)
                    print(file_obj.file.url)
                    file_ext = file_obj.file_type.lower()
                    file_types.append(file_ext)
                except Exception as e:
                    logger.error(f"Error retrieving file: {str(e)}")


            unsupported_files = get_unsupported_files(file_types)
            if unsupported_files:
                from apps.llm_manager.utils.file_types import SUPPORTED_FILE_TYPES
                error_msg = f"Unsupported file type(s): {', '.join(unsupported_files)}. Supported types: {', '.join(SUPPORTED_FILE_TYPES.keys())}"
                logger.warning(error_msg)
                return Response({"message": error_msg}, status=status.HTTP_400_BAD_REQUEST)

        try:
            if file_paths:
                logger.info(f"Sending request with {len(file_paths)} file attachments")
                ai_response = ai_handler.send_text_request_with_files(prompt_data, file_paths)
            else:
                ai_response = ai_handler.send_text_request(prompt_data)

            if ai_response is None:
                return Response({"message": "Error processing files or retrieving AI response"}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error sending request to AI: {str(e)}")
            return Response({"message": f"Error: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)


        ai_response_obj = self.conversation_service.save_ai_response(conversation, "", user_message, model)

        def generate():
            output_tokens = 0
            full_content = []
            error_occurred = False

            try:
                yield json.dumps({"data": format_ai_response(ai_response_obj)}) + "\n"

                for chunk in ai_response:
                    try:
                        if chunk.choices[0].delta.content:
                            content_chunk = chunk.choices[0].delta.content
                            full_content.append(content_chunk)

                            output_tokens += ai_handler.count_tokens([{"role": "assistant", "content": content_chunk}])

                            yield json.dumps({"data": {"chunk": content_chunk}}) + "\n"
                    except Exception as chunk_error:
                        logger.error(f"Error processing chunk: {chunk_error}")
                        error_occurred = True
                        break

                if not error_occurred:
                    yield json.dumps({"data": ["DONE"]}) + "\n"

            except Exception as e:
                logger.error(f"Streaming error: {e}")
                error_occurred = True

            finally:
                try:
                    ai_response_obj.content = "".join(full_content)
                    ai_response_obj.used_tokens = output_tokens
                    ai_response_obj.save()

                    # Update both message and token usage
                    message_usage = self.message_usage_service.update_daily_usage(request.user)
                    token_usage = self.token_usages_service.update_daily_usages(request.user, input_tokens, output_tokens)

                    # Log the updated usage
                    logger.info(f"Updated usage for user {request.user.id}: messages={message_usage.message_count}, tokens={token_usage} (input={input_tokens}, output={output_tokens})")
                except Exception as final_error:
                    logger.error(f"Error finalizing AI response: {final_error}")

                # If an error occurred, return an error message instead of "DONE"
                if error_occurred:
                    yield json.dumps({"data": ["ERROR"]}) + "\n"

        return StreamingHttpResponse(generate(), content_type="application/json")


class AnonymousAIConversationView(PublicApiView):
    """
    Handles AI conversations for users who are not logged in.
    Stores conversation history in Redis.
    """
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.llm_model_service = LLMModelsService()
        self.ai_factory_service = AIHandlerFactory()
        self.system_setting_service = SystemSettingService()
        self.plan_service = PlanService()

    def post(self, request):
        """
        Handles user input and retrieves AI response while maintaining context in Redis.
        """
        serializer = PromptSerializer(data=request.data)
        if not serializer.is_valid():
            first_error_message = serializer.errors[next(iter(serializer.errors))][0]
            result = {
                "message": first_error_message,
                "errors": serializer.errors
            }
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        valid_data = serializer.validated_data

        ip_address = request.META.get('REMOTE_ADDR', 'unknown')
        device_info, _ = get_device_info(request)

        # Generate a unique cache key
        cache_key = f"chat_limit:{ip_address}:{device_info}"
        chat_count = cache.get(cache_key, 0)
        # Check if the device/IP exceeded the limit
        if chat_count >= MAX_CHAT_LIMIT_FOR_ANNONYMOUS_USER:
            return Response(
                {"message": f"Daily message limit reached ({chat_count}/{MAX_CHAT_LIMIT_FOR_ANNONYMOUS_USER}). Please try again tomorrow."},
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )

        user_prompt = valid_data.get("message")
        code = valid_data.get("model")
        model = self.llm_model_service.get(code=code)
        if not model:
            return Response({"message": "Invalid AI model selected"}, status=400)

        # For anonymous users, restrict to gpt-4o-mini if not already selected
        if model.code != "gpt-4o-mini":
            model = self.llm_model_service.get(code="gpt-4o-mini")
            if not model:
                return Response({"message": "Default model not available"}, status=400)

        # Get conversation ID from request (or generate a new one)
        conversation_id = valid_data.get("conversation_id") or str(uuid.uuid4())
        msg_id = str(uuid.uuid4())
        # Retrieve existing conversation from Redis
        redis_key = f"chat:{conversation_id}"
        conversation_history = cache.get(redis_key, [])

        # Append new user message
        message_data = {
            "role": "user",
            "content": user_prompt["content"]["parts"][0],
        }
        conversation_history.append(message_data)

        # Send last 3 conversation history to AI
        ai_handler = self.ai_factory_service.get_handler(model.code)

        # Count tokens for anonymous users too
        input_tokens = ai_handler.count_tokens(conversation_history[-3:])

        # Check token usage for anonymous users
        token_key = f"token_usage:{ip_address}:{device_info}"
        current_usage = cache.get(token_key, {"input": 0, "output": 0, "total": 0})

        # Get token limit from free plan for anonymous users
        free_plan = self.plan_service.get(code="free")
        anon_token_limit = free_plan.tokens_included // 10 if free_plan else 5000  # 10% of free plan limit

        # Check if anonymous user has exceeded token limit
        if current_usage["total"] + input_tokens > anon_token_limit:
            result = {
                "message": f"You've reached the token limit for anonymous users ({anon_token_limit} tokens). Please sign up to continue using our AI services."
            }
            return Response(result, status=status.HTTP_403_FORBIDDEN)

        # Anonymous users cannot upload files, but we'll add a check just in case
        user_message_data = valid_data.get("message", {})
        if user_message_data and "metadata" in user_message_data and "attachments" in user_message_data["metadata"]:
            result = {
                "message": "File uploads are not supported for anonymous users. Please sign up to use this feature."
            }
            return Response(result, status=status.HTTP_403_FORBIDDEN)
        
        # Send request to AI
        ai_response = ai_handler.send_text_request(conversation_history[-3:])
        if not ai_response:
            return Response({"message": "Error retrieving AI response"}, status=status.HTTP_400_BAD_REQUEST)

        def generate():
            output_tokens = 0
            full_content = []

            yield json.dumps({"data": format_ai_response("", from_redis=True, conversation_id=conversation_id, msg_id=msg_id)}) + "\n"

            for chunk in ai_response:
                if chunk.choices[0].delta.content:
                    content_chunk = chunk.choices[0].delta.content
                    full_content.append(content_chunk)
                    # Count tokens for each chunk
                    output_tokens += ai_handler.count_tokens([{"role": "assistant", "content": content_chunk}])
                    yield json.dumps({"data": {"chunk": content_chunk }}) + "\n"

            yield json.dumps({"data": ["DONE"]}) + "\n"

            # Save the full response in Redis
            full_response_text = "".join(full_content)
            ai_message = {"role": "assistant", "content": full_response_text}
            conversation_history.append(ai_message)
            system_setting = self.system_setting_service.get()

            # Store token counts in Redis for anonymous users
            token_key = f"token_usage:{ip_address}:{device_info}"
            current_usage = cache.get(token_key, {"input": 0, "output": 0, "total": 0})
            current_usage["input"] += input_tokens
            current_usage["output"] += output_tokens
            current_usage["total"] = current_usage["input"] + current_usage["output"]
            cache.set(token_key, current_usage, timeout=BLOCK_TIME_FOR_USER)
            logger.info(f"Anonymous user token usage updated: {current_usage}")

            cache.set(redis_key, conversation_history, timeout=system_setting.cache_expiry_time)

        cache.set(cache_key, chat_count + 1, timeout=BLOCK_TIME_FOR_USER)

        return StreamingHttpResponse(generate(), content_type="application/json")
